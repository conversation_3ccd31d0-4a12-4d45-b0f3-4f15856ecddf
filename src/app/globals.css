@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Impact font */
@import url('https://fonts.googleapis.com/css2?family=Impact&display=swap');

:root {
  --background: #000000;
  --foreground: #ffffff;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Arial Narrow Bold', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  letter-spacing: -1px;
}

/* Xavier chaos utilities */
@layer utilities {
  .xavier-border {
    border: 4px solid #ffffff;
  }

  .xavier-border-red {
    border: 4px solid #ff0000;
  }

  .xavier-border-dashed {
    border: 4px dashed #ffffff;
  }

  .xavier-border-dashed-red {
    border: 4px dashed #ff0000;
  }

  .xavier-text-chaos {
    text-transform: uppercase;
    font-weight: 900;
    letter-spacing: -1px;
  }

  .xavier-bg-red {
    background-color: #ff0000;
  }

  .xavier-bg-black {
    background-color: #000000;
  }

  .xavier-diagonal-line {
    background: linear-gradient(to right top, transparent calc(50% - 1px), rgba(255, 0, 0, 0.7) calc(50% - 1px), rgba(255, 0, 0, 0.7) calc(50% + 1px), transparent calc(50% + 1px));
  }
}