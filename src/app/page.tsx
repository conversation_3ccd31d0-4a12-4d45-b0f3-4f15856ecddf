"use client";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {

  return (
    <Providers>
      <div className="min-h-screen bg-xavier-black overflow-hidden">
        {/* Xavier Grid Layout */}
        <div className="h-screen grid grid-cols-1 md:grid-cols-12 grid-rows-12 gap-2 p-2">

          {/* Top Header - Spans full width */}
          <div className="col-span-1 md:col-span-12 row-span-1 md:row-span-1 xavier-bg-red xavier-border flex flex-col items-center justify-center relative w-full">
            <h1 className="text-white text-lg sm:text-xl md:text-2xl lg:text-3xl xavier-text-chaos text-center font-impact tracking-wide">
              $XAVIER RENEGADE ANGEL BRIDGE
            </h1>
            <h2 className="text-white text-base sm:text-lg md:text-xl lg:text-2xl xavier-text-chaos text-center font-impact tracking-wide mt-1">
              CAN YOU BRIDGE THE TRUTH ?????
            </h2>
          </div>

          {/* Mobile Layout: Top Row - Two Small Images */}
          <div className="col-span-1 md:hidden row-span-2 flex gap-2">
            {/* Xavier Meme 1 */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavier.jpg"
                alt="Xavier Meme"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Xavier Experience Image */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavierexp.jpg"
                alt="Xavier Experience"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Center Column - Bridge Interface (Largest) */}
          <div className="col-span-1 md:col-span-6 md:col-start-4 row-span-7 md:row-span-10">
            <div className="h-full xavier-bg-black xavier-border flex items-center justify-center p-2">
              <div className="w-full h-full">
                <BridgeInterface />
              </div>
            </div>
          </div>

          {/* Mobile Layout: Bottom Row - Two Small Images */}
          <div className="col-span-1 md:hidden row-span-2 flex gap-2">
            {/* Xavier Learn First Image */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavierlearnfirst.jpg"
                alt="Xavier Learn First"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Xavier Meme 2 */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavier5g.jpg"
                alt="Xavier 5G Meme"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Desktop Layout: Left Column - Memes and Text */}
          <div className="hidden md:flex md:col-span-3 row-span-10 flex-col gap-2">
            {/* Xavier Meme 1 */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavier.jpg"
                alt="Xavier Meme"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Xavier Experience Image */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavierexp.jpg"
                alt="Xavier Experience"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Desktop Layout: Right Column - Memes and Text */}
          <div className="hidden md:flex md:col-span-3 row-span-10 flex-col gap-2">
            {/* Xavier Learn First Image */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavierlearnfirst.jpg"
                alt="Xavier Learn First"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Xavier Meme 2 */}
            <div className="flex-1 xavier-border">
              <img
                src="/xavier5g.jpg"
                alt="Xavier 5G Meme"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

        </div>
      </div>
    </Providers>
  );
}
