"use client";
import { useWallet } from "@solana/wallet-adapter-react";
import { useWalletModal } from "@solana/wallet-adapter-react-ui";
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useConnect, useDisconnect } from "wagmi";
import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { useState, useEffect, useCallback, useMemo } from "react";
import { EndpointId } from "@layerzerolabs/lz-definitions";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { Connection, PublicKey, TransactionInstruction, AddressLookupTableAccount } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { fromWeb3JsPublicKey, toWeb3JsTransaction, toWeb3JsPublicKey, toWeb3JsInstruction } from '@metaplex-foundation/umi-web3js-adapters';
import { findAssociatedTokenPda, createSplAssociatedTokenProgram, setComputeUnitLimit, setComputeUnitPrice, fetchAddressLookupTable } from '@metaplex-foundation/mpl-toolbox';
import type { AddressLookupTableInput, Instruction, TransactionBuilder, PublicKey as UmiPublicKey } from '@metaplex-foundation/umi';
import bs58 from 'bs58';
import { useReadContract } from 'wagmi';
import { erc20Abi, parseUnits } from 'viem';


const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");
const ETHEREUM_MAINNET_EID = EndpointId.ETHEREUM_V2_MAINNET;
const SOLANA_MAINNET_EID = EndpointId.SOLANA_V2_MAINNET;

const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

// Address Lookup Table mapping for different endpoint IDs
const LOOKUP_TABLE_ADDRESS: Record<number, string> = {
  [EndpointId.SOLANA_V2_MAINNET]: "AokBxha6VMLLgf97B5VYHEtqztamWmYERBmmFvjuTzJB", // Replace with actual LUT address
  [EndpointId.SOLANA_V2_TESTNET]: "AokBxha6VMLLgf97B5VYHEtqztamWmYERBmmFvjuTzJB", // Replace with actual LUT address
};

// Helper function to format endpoint ID for error messages
const formatEid = (eid: EndpointId): string => {
  switch (eid) {
    case EndpointId.SOLANA_V2_MAINNET:
      return "Solana Mainnet";
    case EndpointId.SOLANA_V2_TESTNET:
      return "Solana Testnet";
    case EndpointId.ETHEREUM_V2_MAINNET:
      return "Ethereum Mainnet";
    default:
      return `EID ${eid}`;
  }
};

// Transaction types for compute unit estimation
enum TransactionType {
  SendOFT = 'SendOFT',
  Quote = 'Quote',
}

// Hardcoded compute unit estimates for different transaction types
const TransactionCuEstimates: Record<TransactionType, number> = {
  [TransactionType.SendOFT]: 250_000, // Increased from 400k to 600k for LayerZero OFT transactions
  [TransactionType.Quote]: 200_000,
};

const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};

const fetchGasPrice = async (): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> => {
  try {
    const response = await fetch('https://api.blocknative.com/gasprices/blockprices?chainid=1');
    const data = await response.json();

    if (data.blockPrices && data.blockPrices.length > 0) {
      const estimatedPrices = data.blockPrices[0].estimatedPrices;
      const price90 = estimatedPrices.find((p: { confidence: number; maxFeePerGas: number; maxPriorityFeePerGas: number }) => p.confidence === 90);

      if (price90) {
        const maxFeePerGas = BigInt(Math.ceil(price90.maxFeePerGas * 1e9));
        const maxPriorityFeePerGas = BigInt(Math.ceil(price90.maxPriorityFeePerGas * 1e9));

        return { maxFeePerGas, maxPriorityFeePerGas };
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
};

const ETHEREUM_GAS_LIMIT = BigInt(250000);

const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

const isValidSolanaAddress = (address: string): boolean => {
  try {
    const decoded = bs58.decode(address);
    return decoded.length === 32;
  } catch {
    return false;
  }
};

// Address Lookup Table utility function
const getAddressLookupTable = async (connection: Connection, umi: ReturnType<typeof createUmi>, fromEid: EndpointId) => {
  // Lookup Table Address and Priority Fee Calculation
  const lookupTableAddress = LOOKUP_TABLE_ADDRESS[fromEid];
  if (!lookupTableAddress) {
    throw new Error(`No lookup table found for ${formatEid(fromEid)}`);
  }

  const lookupTablePublicKey = publicKey(lookupTableAddress);
  const addressLookupTableInput: AddressLookupTableInput = await fetchAddressLookupTable(umi, lookupTablePublicKey);
  if (!addressLookupTableInput) {
    throw new Error(`No address lookup table found for ${lookupTableAddress}`);
  }

  const { value: lookupTableAccount } = await connection.getAddressLookupTable(toWeb3JsPublicKey(lookupTablePublicKey));
  if (!lookupTableAccount) {
    throw new Error(`No address lookup table account found for ${lookupTableAddress}`);
  }

  return {
    lookupTableAddress: lookupTablePublicKey,
    addressLookupTableInput,
    lookupTableAccount,
  };
};

// Helper function to get prioritization fees
const getPrioritizationFees = async (connection: Connection) => {
  try {
    const recentPrioritizationFees = await connection.getRecentPrioritizationFees();
    const fees = recentPrioritizationFees.map(fee => fee.prioritizationFee).filter(fee => fee > 0);
    const averageFeeExcludingZeros = fees.length > 0 ? fees.reduce((a, b) => a + b, 0) / fees.length : 1000;
    return { averageFeeExcludingZeros };
  } catch (error) {
    console.warn('Failed to get prioritization fees, using default:', error);
    return { averageFeeExcludingZeros: 1000 };
  }
};

// Helper function to simulate transaction and get compute units
const getSimulationComputeUnits = async (
  connection: Connection,
  instructions: TransactionInstruction[],
  payer: PublicKey,
  lookupTableAccounts: AddressLookupTableAccount[]
) => {
  try {
    const testTransaction = new (await import('@solana/web3.js')).VersionedTransaction(
      new (await import('@solana/web3.js')).TransactionMessage({
        payerKey: payer,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
        instructions,
      }).compileToV0Message(lookupTableAccounts)
    );

    const simulation = await connection.simulateTransaction(testTransaction);
    return simulation.value.unitsConsumed || TransactionCuEstimates[TransactionType.SendOFT];
  } catch (error) {
    console.warn('Failed to simulate transaction, using default compute units:', error);
    return TransactionCuEstimates[TransactionType.SendOFT];
  }
};

// Get compute unit price and limit
const getComputeUnitPriceAndLimit = async (
  connection: Connection,
  ixs: Instruction[],
  wallet: { publicKey: UmiPublicKey }, // Simplified wallet interface
  lookupTableAccount: AddressLookupTableAccount,
  transactionType: TransactionType
) => {
  const { averageFeeExcludingZeros } = await getPrioritizationFees(connection);
  const priorityFee = Math.round(averageFeeExcludingZeros);
  const computeUnitPrice = BigInt(priorityFee);

  let computeUnits;

  try {
    const simulatedUnits = await getSimulationComputeUnits(
      connection,
      ixs.map((ix) => toWeb3JsInstruction(ix)),
      toWeb3JsPublicKey(wallet.publicKey),
      [lookupTableAccount]
    );
    // Use the higher of simulated units or our hardcoded estimate for safety
    computeUnits = Math.max(simulatedUnits, TransactionCuEstimates[transactionType]);
  } catch (e) {
    console.error(`Error retrieving simulations compute units from RPC:`, e);
    console.log(
      `Falling back to hardcoded estimate for ${transactionType}: ${TransactionCuEstimates[transactionType]} CUs`
    );
    computeUnits = TransactionCuEstimates[transactionType];
  }

  if (!computeUnits) {
    throw new Error('Unable to compute units');
  }

  // Ensure we have a minimum of 400k compute units for LayerZero transactions
  const minComputeUnits = 400_000;
  computeUnits = Math.max(computeUnits, minComputeUnits);

  return {
    computeUnitPrice,
    computeUnits,
  };
};

// Add compute unit instructions with address lookup table
const addComputeUnitInstructions = async (
  connection: Connection,
  umi: ReturnType<typeof createUmi>,
  eid: EndpointId,
  txBuilder: TransactionBuilder,
  umiWalletSigner: { publicKey: UmiPublicKey }, // Simplified wallet interface
  computeUnitPriceScaleFactor: number,
  transactionType: TransactionType
) => {
  const computeUnitLimitScaleFactor = 1.3; // Increased to 1.3 for more conservative estimates
  const { addressLookupTableInput, lookupTableAccount } = await getAddressLookupTable(connection, umi, eid);
  const { computeUnitPrice, computeUnits } = await getComputeUnitPriceAndLimit(
    connection,
    txBuilder.getInstructions(),
    umiWalletSigner,
    lookupTableAccount,
    transactionType
  );

  // Since transaction builders are immutable, we must be careful to always assign the result
  const newTxBuilder = transactionBuilder()
    .add(
      setComputeUnitPrice(umi, {
        microLamports: computeUnitPrice * BigInt(Math.floor(computeUnitPriceScaleFactor)),
      })
    )
    .add(setComputeUnitLimit(umi, { units: computeUnits * computeUnitLimitScaleFactor }))
    .setAddressLookupTables([addressLookupTableInput])
    .add(txBuilder);

  return newTxBuilder;
};

const oftAbi = [
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      { "internalType": "bool", "name": "_payInLzToken", "type": "bool" }
    ],
    "name": "quoteSend",
    "outputs": [
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "msgFee",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "_fee",
        "type": "tuple"
      },
      { "internalType": "address", "name": "_refundAddress", "type": "address" }
    ],
    "name": "send",
    "outputs": [
      {
        "components": [
          { "internalType": "bytes32", "name": "guid", "type": "bytes32" },
          { "internalType": "uint64", "name": "nonce", "type": "uint64" },
          {
            "components": [
              { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
              { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
            ],
            "internalType": "struct MessagingFee",
            "name": "fee",
            "type": "tuple"
          }
        ],
        "internalType": "struct MessagingReceipt",
        "name": "msgReceipt",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "amountSentLD", "type": "uint256" },
          { "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256" }
        ],
        "internalType": "struct OFTReceipt",
        "name": "oftReceipt",
        "type": "tuple"
      }
    ],
    "stateMutability": "payable",
    "type": "function"
  }
] as const;

interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
  gasPrice: { maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null;
  customEthAddress: string;
  customSolanaAddress: string;
}



export default function BridgeInterface() {
  const solanaWallet = useWallet();
  const { setVisible: setSolanaWalletModalVisible } = useWalletModal();
  const { address: ethAddress, isConnected: isEthConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { connect, connectors, isPending: isConnectPending } = useConnect();

  const [isClient, setIsClient] = useState(false);
  const [isEthWalletModalOpen, setIsEthWalletModalOpen] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [direction, setDirection] = useState<'sol-to-eth' | 'eth-to-sol'>('sol-to-eth');
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
    gasPrice: null,
    customEthAddress: '',
    customSolanaAddress: '',
  });


  const umi = useMemo(() => {
    const umiInstance = createUmi(SOLANA_RPC_URL);
    if (solanaWallet.wallet) {
      umiInstance.use(walletAdapterIdentity(solanaWallet));
    }
    umiInstance.programs.add(createSplAssociatedTokenProgram());
    return umiInstance;
  }, [solanaWallet]);

  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending, error: ethTxError } = useWriteContract();
  const { isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  const fetchSolanaBalance = useCallback(async () => {
    if (!solanaWallet.publicKey || !SOLANA_OFT_MINT_ADDRESS || !solanaWallet.connected) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(solanaWallet.publicKey.toString());

      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey, solanaWallet.connected]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  useEffect(() => {
    if (ethTxHash && isEthTxSuccess) {
      const layerZeroScanLink = getLayerZeroScanLink(ethTxHash, false);



      setBridgeState(prev => ({
        ...prev,
        txHash: ethTxHash,
        layerZeroScanLink,
        isLoading: false
      }));
    }
  }, [ethTxHash, isEthTxSuccess, amount]);

  useEffect(() => {
    if (isEthTxPending) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }
  }, [isEthTxPending]);

  useEffect(() => {
    if (ethTxError) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: ethTxError.message || "Transaction failed"
      }));
    }
  }, [ethTxError]);

  // Add effect to reset button state when amount changes
  useEffect(() => {
    setBridgeState(prev => ({
      ...prev,
      nativeFee: null,
      txHash: null,
      layerZeroScanLink: null,
      error: null,
    }));
  }, [amount]);

  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
      gasPrice: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    if (direction === 'sol-to-eth') {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      if (!ethAddress && !bridgeState.customEthAddress) {
        throw new Error("Please connect your Ethereum wallet or enter a recipient address.");
      }
      if (bridgeState.customEthAddress && !isValidEthereumAddress(bridgeState.customEthAddress)) {
        throw new Error("Please enter a valid Ethereum address.");
      }
    } else {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      if (!solanaWallet.publicKey && !bridgeState.customSolanaAddress) {
        throw new Error("Please connect your Solana wallet or enter a recipient address.");
      }
      if (bridgeState.customSolanaAddress && !isValidSolanaAddress(bridgeState.customSolanaAddress)) {
        throw new Error("Please enter a valid Solana address.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }

    // Check if amount exceeds balance
    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      if (amountNum > solanaBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.solanaBalance || '0'} tokens.`);
      }
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      if (amountNum > ethereumBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.ethereumBalance || '0'} tokens.`);
      }
    }
  }, [direction, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount, bridgeState.customEthAddress, bridgeState.customSolanaAddress, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const quoteSolanaToEthereum = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));

      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey!),
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
        },
        [],
        (await getAddressLookupTable(connection, umi, SOLANA_MAINNET_EID)).lookupTableAddress
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Solana has 6 decimals, Ethereum has 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, ethAddress, solanaWallet.publicKey, umi, bridgeState.customEthAddress]);

  const executeSolanaToEthereum = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteSolanaToEthereum();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));
      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get token account
      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(SOLANA_OFT_MINT_ADDRESS!)),
        owner: publicKey(solanaWallet.publicKey!),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: bridgeState.nativeFee,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Build transaction with address lookup table and compute units
      let txB = transactionBuilder().add([ix]);
      txB = await addComputeUnitInstructions(
        connection,
        umi,
        SOLANA_MAINNET_EID,
        txB,
        umi.identity,
        4, // computeUnitPriceScaleFactor
        TransactionType.SendOFT
      );

      const {
        context: { slot: minContextSlot },
        value: { blockhash, lastValidBlockHeight }
      } = await connection.getLatestBlockhashAndContext('finalized');

      txB = txB.setBlockhash(blockhash);

      const umiTx = txB.build(umi);
      const web3Tx = toWeb3JsTransaction(umiTx);

      const signature = await solanaWallet.sendTransaction(web3Tx, connection, { minContextSlot });
      const txHash = signature;

      await connection.confirmTransaction({ blockhash, lastValidBlockHeight, signature });
      const layerZeroScanLink = getLayerZeroScanLink(txHash, false);



      setBridgeState(prev => ({
        ...prev,
        txHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Refresh balance after successful transaction
      if (solanaWallet.connected && solanaWallet.publicKey) {
        const newBalance = await fetchSolanaBalance();
        setBridgeState(prev => ({ ...prev, solanaBalance: newBalance }));
      }
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteSolanaToEthereum, amount, ethAddress, solanaWallet.publicKey, umi, fetchSolanaBalance, solanaWallet.connected, bridgeState.customEthAddress]);

  const quoteEthereumToSolana = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Get current gas prices from Blocknative
      const gasPrice = await fetchGasPrice();

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = ETHEREUM_GAS_LIMIT * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = BigInt(20 * 1e9); // 20 gwei
        nativeFee = ETHEREUM_GAS_LIMIT * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Ethereum has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        gasPrice,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, solanaWallet.publicKey, bridgeState.customSolanaAddress]);

  const executeEthereumToSolana = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteEthereumToSolana();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Use custom address if provided, otherwise use connected wallet address
      const recipientSolanaAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey!.toString();
      const solanaAddressBytes = bs58.decode(recipientSolanaAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: SOLANA_MAINNET_EID,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: bridgeState.nativeFee,
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const gasPrice = await fetchGasPrice();

      // Execute the actual contract transaction with optimized gas
      writeOftContract({
        address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: bridgeState.nativeFee,
        gas: ETHEREUM_GAS_LIMIT,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      // The transaction hash will be available in ethTxHash after the transaction is submitted
      // We'll handle the success case in a useEffect that watches for ethTxHash changes

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteEthereumToSolana, amount, solanaWallet.publicKey, ethAddress, writeOftContract, bridgeState.customSolanaAddress]);

  const hasInsufficientBalance = useMemo(() => {
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) return false;

    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      return amountNum > solanaBalance;
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      return amountNum > ethereumBalance;
    }
  }, [direction, amount, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const canQuote = useMemo(() => {
    const basicRequirements = direction === 'sol-to-eth'
      ? solanaWallet.connected && (ethAddress || bridgeState.customEthAddress)
      : isEthConnected && (solanaWallet.publicKey || bridgeState.customSolanaAddress);

    return basicRequirements && !hasInsufficientBalance;
  }, [direction, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey, bridgeState.customEthAddress, bridgeState.customSolanaAddress, hasInsufficientBalance]);



  if (!isClient) return null;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Bridge Interface Container */}
      <div className="xavier-bg-red p-2 md:p-3 xavier-border flex-1 flex flex-col relative">

        {/* Token Exchange Section */}
        <div className="xavier-bg-black p-2 md:p-3 xavier-border flex flex-col gap-2 md:gap-3 mb-2 md:mb-3">
          {/* From Token Section */}
          <div className="bg-blue-900 p-1 md:p-2 xavier-border">
            <div className="flex justify-between items-center mb-1 md:mb-2">
              <span className="text-white text-xs md:text-sm font-impact tracking-wide">FROM</span>
              <span className="text-white text-xs md:text-sm font-impact tracking-wide">
                BAL: {direction === 'sol-to-eth' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
              </span>
            </div>
            <div className="flex items-center gap-1 md:gap-2">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.0"
                className="bg-xavier-black text-white text-base md:text-xl font-impact outline-none flex-1 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none p-1 md:p-2 xavier-border tracking-wide"
                step="0.000001"
                min="0"
              />
              <div className="flex items-center space-x-1 md:space-x-2 xavier-bg-black xavier-border px-1 md:px-2 py-1 md:py-2">
                <img
                  src={direction === 'sol-to-eth' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                  alt={direction === 'sol-to-eth' ? 'Solana' : 'Ethereum'}
                  className="w-3 md:w-4 h-3 md:h-4"
                />
                <img
                  src="/tokenIcon.png"
                  alt="XAVIER Token"
                  className="w-3 md:w-4 h-3 md:h-4"
                />
                <span className="text-white font-impact text-xs tracking-wide">XAVIER</span>
              </div>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex items-center justify-center py-1 md:py-2">
            <button
              onClick={() => {
                setDirection(direction === 'sol-to-eth' ? 'eth-to-sol' : 'sol-to-eth');
                resetBridgeState();
              }}
              className="bg-blue-800 hover:bg-blue-700 p-2 md:p-3 transition-colors xavier-border relative"
            >
              <svg className="w-4 md:w-5 h-4 md:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M8 7h12m0 0l-4-4m4 4l-4 4m-4 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </button>
          </div>

          {/* To Token Section */}
          <div className="bg-blue-900 p-1 md:p-2 xavier-border">
            <div className="flex justify-between items-center mb-1 md:mb-2">
              <span className="text-white text-xs md:text-sm font-impact tracking-wide">TO</span>
              <span className="text-white text-xs md:text-sm font-impact tracking-wide">
                BAL: {direction === 'eth-to-sol' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
              </span>
            </div>
            <div className="flex items-center gap-1 md:gap-2">
              <div className="text-white text-base md:text-xl font-impact flex-1 overflow-hidden text-ellipsis p-1 md:p-2 xavier-border xavier-bg-black tracking-wide">
                {bridgeState.receiveAmount || '0.0'}
              </div>
              <div className="flex items-center space-x-1 md:space-x-2 xavier-bg-black xavier-border px-1 md:px-2 py-1 md:py-2">
                <img
                  src={direction === 'eth-to-sol' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                  alt={direction === 'eth-to-sol' ? 'Solana' : 'Ethereum'}
                  className="w-3 md:w-4 h-3 md:h-4"
                />
                <img
                  src="/tokenIcon.png"
                  alt="XAVIER Token"
                  className="w-3 md:w-4 h-3 md:h-4"
                />
                <span className="text-white font-impact text-xs tracking-wide">XAVIER</span>
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Connection Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-1 md:gap-2 mb-2 md:mb-3">
          {/* Solana Wallet */}
          <div className="flex items-center justify-between p-1 md:p-2 xavier-bg-black xavier-border">
            <div className="flex items-center space-x-1 md:space-x-2">
              <img src="/solana-sol-logo.svg" alt="Solana" className="w-4 md:w-5 h-4 md:h-5" />
              <span className="text-white font-impact text-xs md:text-sm tracking-wide">SOLANA</span>
            </div>
            {solanaWallet.connected ? (
              <div className="flex items-center space-x-1 md:space-x-2">
                <span className="text-white text-xs font-impact tracking-wide">
                  {solanaWallet.publicKey?.toString().slice(0, 4)}...{solanaWallet.publicKey?.toString().slice(-4)}
                </span>
                <button
                  onClick={() => solanaWallet.disconnect()}
                  className="bg-red-700 hover:bg-red-600 text-white hover:text-white text-xs font-impact px-1 md:px-2 py-1 xavier-border transition-colors tracking-wide"
                >
                  DISCONNECT
                </button>
              </div>
            ) : (
              <button
                onClick={() => setSolanaWalletModalVisible(true)}
                className="bg-red-700 hover:bg-red-600 text-white text-xs font-impact py-1 px-1 md:px-2 xavier-border transition-colors tracking-wide"
              >
                CONNECT
              </button>
            )}
          </div>

          {/* Ethereum Wallet */}
          <div className="flex items-center justify-between p-1 md:p-2 xavier-bg-black xavier-border">
            <div className="flex items-center space-x-1 md:space-x-2">
              <img src="/ethereum-eth-logo.svg" alt="Ethereum" className="w-4 md:w-5 h-4 md:h-5" />
              <span className="text-white font-impact text-xs md:text-sm tracking-wide">ETHEREUM</span>
            </div>
            {isEthConnected ? (
              <div className="flex items-center space-x-1 md:space-x-2">
                <span className="text-white text-xs font-impact tracking-wide">
                  {ethAddress?.slice(0, 6)}...{ethAddress?.slice(-4)}
                </span>
                <button
                  onClick={() => disconnect()}
                  className="bg-red-700 hover:bg-red-600 text-white hover:text-white text-xs font-impact px-1 md:px-2 py-1 xavier-border transition-colors tracking-wide"
                >
                  DISCONNECT
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsEthWalletModalOpen(true)}
                className="bg-red-700 hover:bg-red-600 text-white text-xs font-impact py-1 px-1 md:px-2 xavier-border transition-colors tracking-wide"
              >
                CONNECT
              </button>
            )}
          </div>
        </div>

        {/* Custom Address Inputs */}
        {direction === 'sol-to-eth' && !ethAddress && (
          <div className="xavier-bg-black p-1 md:p-2 xavier-border mb-1 md:mb-2">
            <div className="text-white font-impact mb-1 text-xs md:text-sm tracking-wide">ENTER ETHEREUM ADDRESS:</div>
            <input
              type="text"
              value={bridgeState.customEthAddress}
              onChange={(e) => setBridgeState(prev => ({ ...prev, customEthAddress: e.target.value }))}
              className="w-full px-1 md:px-2 py-1 md:py-2 bg-blue-900 text-white xavier-border placeholder-white font-impact focus:outline-none text-xs md:text-sm tracking-wide"
              placeholder="0x..."
            />
          </div>
        )}

        {direction === 'eth-to-sol' && !solanaWallet.publicKey && (
          <div className="xavier-bg-black p-1 md:p-2 xavier-border mb-1 md:mb-2">
            <div className="text-white font-impact mb-1 text-xs md:text-sm tracking-wide">ENTER SOLANA ADDRESS:</div>
            <input
              type="text"
              value={bridgeState.customSolanaAddress}
              onChange={(e) => setBridgeState(prev => ({ ...prev, customSolanaAddress: e.target.value }))}
              className="w-full px-1 md:px-2 py-1 md:py-2 bg-blue-900 text-white xavier-border placeholder-white font-impact focus:outline-none text-xs md:text-sm tracking-wide"
              placeholder="SOLANA ADDRESS..."
            />
          </div>
        )}

        {/* Error Display */}
        {bridgeState.error && (
          <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md p-2 md:p-4 bg-red-700 xavier-border animate-glitch">
            <p className="text-white font-impact text-xs md:text-sm tracking-wide">{bridgeState.error}</p>
          </div>
        )}

        {/* Action Button */}
        <div className="flex-1">
          {direction === 'sol-to-eth' ? (
            <button
              onClick={async () => {
                if (!bridgeState.nativeFee) {
                  await quoteSolanaToEthereum();
                } else {
                  await executeSolanaToEthereum();
                }
              }}
              disabled={!canQuote || bridgeState.isLoading || !!bridgeState.txHash}
              className="w-full py-2 md:py-4 px-2 md:px-4 bg-blue-800 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white hover:text-white font-impact transition-all duration-200 flex items-center justify-center xavier-border text-sm md:text-lg relative overflow-hidden tracking-wide"
            >
              {bridgeState.isLoading && (
                <div className="absolute inset-0 xavier-diagonal-line animate-pulse"></div>
              )}
              <div className="relative z-10">
                {bridgeState.isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-1 md:mr-2 h-4 md:h-5 w-4 md:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-xs md:text-sm tracking-wide">{bridgeState.nativeFee ? 'SENDING...' : 'QUOTE...'}</span>
                  </div>
                ) : bridgeState.txHash ? (
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs md:text-sm tracking-wide">BRIDGED!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white hover:text-blue-300 text-xs underline tracking-wide"
                        onClick={(e) => e.stopPropagation()}
                      >
                        VIEW TX ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs md:text-sm tracking-wide">BRIDGE NOW!</span>
                    <span className="text-xs text-white tracking-wide">
                      FEE: {(Number(bridgeState.nativeFee) / 1e9).toFixed(4)} SOL
                    </span>
                  </div>
                ) : (
                  <span className="text-xs md:text-sm tracking-wide">GET QUOTE & BRIDGE</span>
                )}
              </div>
            </button>
          ) : (
            <button
              onClick={async () => {
                if (!bridgeState.nativeFee) {
                  await quoteEthereumToSolana();
                } else {
                  await executeEthereumToSolana();
                }
              }}
              disabled={!canQuote || bridgeState.isLoading || !!bridgeState.txHash}
              className="w-full py-2 md:py-4 px-2 md:px-4 bg-blue-800 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white hover:text-white font-impact transition-all duration-200 flex items-center justify-center xavier-border text-sm md:text-lg relative overflow-hidden tracking-wide"
            >
              {bridgeState.isLoading && (
                <div className="absolute inset-0 xavier-diagonal-line animate-pulse"></div>
              )}
              <div className="relative z-10">
                {bridgeState.isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-1 md:mr-2 h-4 md:h-5 w-4 md:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-xs md:text-sm tracking-wide">{bridgeState.nativeFee ? 'SENDING...' : 'QUOTE...'}</span>
                  </div>
                ) : bridgeState.txHash ? (
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs md:text-sm tracking-wide">BRIDGED!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white hover:text-blue-300 text-xs underline tracking-wide"
                        onClick={(e) => e.stopPropagation()}
                      >
                        VIEW TX ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs md:text-sm tracking-wide">BRIDGE NOW!</span>
                    <span className="text-xs text-white tracking-wide">
                      FEE: {(Number(bridgeState.nativeFee) / 1e18).toFixed(4)} ETH
                    </span>
                  </div>
                ) : (
                  <span className="text-xs md:text-sm tracking-wide">GET QUOTE & BRIDGE</span>
                )}
              </div>
            </button>
          )}
        </div>

        {/* Powered by LayerZero */}
        <div className="flex items-center justify-center mt-1 md:mt-2">
          <div className="flex items-center space-x-1 md:space-x-2 xavier-bg-black xavier-border px-1 md:px-2 py-1">
            <span className="text-white text-xs font-impact tracking-wide">POWERED BY</span>
            <img
              src="/layerzero.svg"
              alt="LayerZero"
              className="h-3 md:h-4 opacity-80"
            />
          </div>
        </div>
      </div>

      {/* Ethereum Wallet Modal */}
      {isEthWalletModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-[100]">
          <div className="bg-blue-900 p-3 md:p-4 max-w-sm w-full mx-4 xavier-border relative">
            <div className="flex justify-between items-center mb-3 md:mb-4">
              <h3 className="text-white font-impact text-xs md:text-sm tracking-wide">CONNECT ETHEREUM</h3>
              <button
                onClick={() => setIsEthWalletModalOpen(false)}
                className="text-white hover:text-blue-300"
              >
                <svg className="w-4 md:w-5 h-4 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-1 md:space-y-2">
              {connectors.map((connector) => (
                <button
                  key={connector.uid}
                  onClick={() => {
                    connect({ connector });
                    setIsEthWalletModalOpen(false);
                  }}
                  disabled={isConnectPending}
                  className="w-full flex items-center space-x-1 md:space-x-2 p-1 md:p-2 xavier-bg-black hover:bg-gray-800 xavier-border transition-colors disabled:opacity-50"
                >
                  <div className="w-5 md:w-6 h-5 md:h-6 bg-red-700 xavier-border flex items-center justify-center">
                    <span className="text-white text-xs font-impact tracking-wide">
                      {connector.name.charAt(0)}
                    </span>
                  </div>
                  <span className="text-white font-impact text-xs md:text-sm tracking-wide">{connector.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
