import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        xavier: {
          black: "#000000",
          red: "#ff0000",
          white: "#ffffff",
          yellow: "#ffff00",
          green: "#00ff00",
          chaos: "#ff0000",
        },
        // Keep chillhouse colors for backward compatibility during transition
        chillhouse: {
          main: "#A75338",
          accent: "#D4CDAF",
          neutral: "#9A9791",
          bg: "#8D8D74",
          ui: "#BDAF88",
        },
      },
      fontFamily: {
        'impact': ['Impact', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Arial Narrow Bold', 'sans-serif'],
      },
      animation: {
        'chaos-pulse': 'chaos-pulse 2s ease-in-out infinite alternate',
        'glitch': 'glitch 0.3s ease-in-out infinite alternate',
      },
      keyframes: {
        'chaos-pulse': {
          '0%': { transform: 'scale(1)', filter: 'hue-rotate(0deg)' },
          '100%': { transform: 'scale(1.05)', filter: 'hue-rotate(10deg)' },
        },
        'glitch': {
          '0%': { transform: 'translate(0)' },
          '20%': { transform: 'translate(-2px, 2px)' },
          '40%': { transform: 'translate(-2px, -2px)' },
          '60%': { transform: 'translate(2px, 2px)' },
          '80%': { transform: 'translate(2px, -2px)' },
          '100%': { transform: 'translate(0)' },
        },
      },
    },
  },
  plugins: [],
} satisfies Config;
